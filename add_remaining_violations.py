#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة باقي الانتهاكات المطلوبة
"""

import sys
import os
import json
from datetime import datetime

# إضافة مسار التطبيق
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal, ViolationType, HumanRightsCategory

def add_remaining_violations():
    """إضافة باقي الانتهاكات"""
    
    db = SessionLocal()
    
    try:
        # جلب معرفات الفئات
        categories = db.query(HumanRightsCategory).all()
        category_map = {cat.name: cat.id for cat in categories}
        
        # باقي الانتهاكات
        remaining_violations = [
            # الحق في الحياة والأمان الشخصي
            {
                "name": "الانفجار",
                "category": "الحق في الحياة والأمان الشخصي",
                "description": "الانفجارات والتفجيرات",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "مكان الانفجار", "required": True},
                    "casualties": {"type": "number", "label": "عدد الضحايا", "required": True},
                    "injured": {"type": "number", "label": "عدد المصابين", "required": False},
                    "explosion_type": {"type": "select", "label": "نوع الانفجار", "options": ["قنبلة", "لغم", "انفجار غاز", "مواد كيميائية", "أخرى"], "required": False},
                    "target": {"type": "text", "label": "الهدف", "required": False}
                })
            },
            {
                "name": "الحرائق",
                "category": "الحق في الحياة والأمان الشخصي",
                "description": "الحرائق والحوادث المرتبطة بها",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "مكان الحريق", "required": True},
                    "casualties": {"type": "number", "label": "عدد الضحايا", "required": False},
                    "injured": {"type": "number", "label": "عدد المصابين", "required": False},
                    "fire_cause": {"type": "select", "label": "سبب الحريق", "options": ["كهربائي", "غاز", "مواد قابلة للاشتعال", "مجهول", "أخرى"], "required": False},
                    "building_type": {"type": "text", "label": "نوع المبنى", "required": False}
                })
            },
            {
                "name": "الغرق",
                "category": "الحق في الحياة والأمان الشخصي",
                "description": "حوادث الغرق",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "مكان الحادث", "required": True},
                    "victims_count": {"type": "number", "label": "عدد الضحايا", "required": True},
                    "water_body": {"type": "select", "label": "نوع المسطح المائي", "options": ["بحر", "نهر", "بحيرة", "بركة", "أخرى"], "required": False},
                    "victim_age": {"type": "text", "label": "أعمار الضحايا", "required": False}
                })
            },
            {
                "name": "الوفاة/الإصابة بالكهرباء",
                "category": "الحق في الحياة والأمان الشخصي",
                "description": "حوادث الكهرباء",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "مكان الحادث", "required": True},
                    "casualties": {"type": "number", "label": "عدد الوفيات", "required": False},
                    "injured": {"type": "number", "label": "عدد المصابين", "required": False},
                    "electrical_source": {"type": "select", "label": "مصدر الكهرباء", "options": ["خط كهرباء", "جهاز كهربائي", "مولد", "أخرى"], "required": False}
                })
            },
            {
                "name": "الإنقاذ",
                "category": "النزاعات والصراعات",
                "description": "عمليات الإنقاذ والإغاثة",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "مكان العملية", "required": True},
                    "rescued_count": {"type": "number", "label": "عدد المنقذين", "required": False},
                    "rescue_type": {"type": "select", "label": "نوع الإنقاذ", "options": ["إنقاذ من حريق", "إنقاذ من غرق", "إنقاذ من انهيار", "إنقاذ طبي", "أخرى"], "required": False},
                    "rescue_team": {"type": "text", "label": "فريق الإنقاذ", "required": False}
                })
            },
            
            # الحق في الحرية والأمان
            {
                "name": "السجون ومراكز الاحتجاز",
                "category": "الحق في الحرية والأمان",
                "description": "أوضاع السجون ومراكز الاحتجاز",
                "required_fields": json.dumps({
                    "facility_name": {"type": "text", "label": "اسم المرفق", "required": True},
                    "location": {"type": "text", "label": "الموقع", "required": True},
                    "detainees_count": {"type": "number", "label": "عدد المحتجزين", "required": False},
                    "issue_type": {"type": "select", "label": "نوع المشكلة", "options": ["اكتظاظ", "سوء معاملة", "نقص في الخدمات", "انتهاك حقوق", "أخرى"], "required": True}
                })
            },
            {
                "name": "وفاة سجين",
                "category": "الحق في الحرية والأمان",
                "description": "وفاة المحتجزين في السجون",
                "required_fields": json.dumps({
                    "prison_name": {"type": "text", "label": "اسم السجن", "required": True},
                    "location": {"type": "text", "label": "الموقع", "required": True},
                    "death_cause": {"type": "select", "label": "سبب الوفاة", "options": ["مرض", "إهمال طبي", "تعذيب", "انتحار", "مجهول"], "required": False},
                    "prisoner_age": {"type": "number", "label": "عمر السجين", "required": False}
                })
            },
            {
                "name": "العفو العام",
                "category": "الحق في الحرية والأمان",
                "description": "قرارات العفو العام",
                "required_fields": json.dumps({
                    "release_date": {"type": "date", "label": "تاريخ الإفراج", "required": True},
                    "released_count": {"type": "number", "label": "عدد المفرج عنهم", "required": True},
                    "amnesty_type": {"type": "select", "label": "نوع العفو", "options": ["عفو عام", "عفو خاص", "إفراج مشروط"], "required": False}
                })
            },
            {
                "name": "حظر التجوال",
                "category": "الحق في الحرية والأمان",
                "description": "فرض حظر التجوال",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "المنطقة", "required": True},
                    "start_time": {"type": "time", "label": "وقت البداية", "required": False},
                    "end_time": {"type": "time", "label": "وقت النهاية", "required": False},
                    "reason": {"type": "textarea", "label": "السبب", "required": False}
                })
            },
            {
                "name": "قطع الطرق",
                "category": "الحق في الحرية والأمان",
                "description": "قطع الطرق والمواصلات",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "الطريق المقطوع", "required": True},
                    "duration": {"type": "text", "label": "مدة القطع", "required": False},
                    "reason": {"type": "select", "label": "السبب", "options": ["احتجاج", "أمني", "صيانة", "حادث", "أخرى"], "required": False}
                })
            },
            
            # الحق في الصحة
            {
                "name": "المؤسسات الصحية",
                "category": "الحق في الصحة والخدمات الطبية",
                "description": "قضايا المؤسسات الصحية",
                "required_fields": json.dumps({
                    "facility_name": {"type": "text", "label": "اسم المؤسسة", "required": True},
                    "location": {"type": "text", "label": "الموقع", "required": True},
                    "issue_type": {"type": "select", "label": "نوع المشكلة", "options": ["نقص في الأدوية", "نقص في الكوادر", "تدهور البنية التحتية", "انقطاع الكهرباء"], "required": True}
                })
            },
            {
                "name": "حرائق المؤسسات الصحية",
                "category": "الحق في الصحة والخدمات الطبية",
                "description": "حرائق في المستشفيات والمراكز الصحية",
                "required_fields": json.dumps({
                    "facility_name": {"type": "text", "label": "اسم المؤسسة", "required": True},
                    "location": {"type": "text", "label": "الموقع", "required": True},
                    "casualties": {"type": "number", "label": "عدد الضحايا", "required": False},
                    "patients_affected": {"type": "number", "label": "عدد المرضى المتأثرين", "required": False}
                })
            },
            
            # الحق في البيئة
            {
                "name": "البيئة والتلوث",
                "category": "الحق في بيئة آمنة",
                "description": "قضايا التلوث البيئي",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "المنطقة المتأثرة", "required": True},
                    "pollution_type": {"type": "select", "label": "نوع التلوث", "options": ["هواء", "ماء", "تربة", "ضوضاء", "إشعاعي"], "required": True},
                    "pollution_source": {"type": "text", "label": "مصدر التلوث", "required": False},
                    "affected_population": {"type": "number", "label": "عدد السكان المتأثرين", "required": False}
                })
            },
            {
                "name": "عمليات الإتلاف",
                "category": "الحق في بيئة آمنة",
                "description": "إتلاف المواد الضارة بيئياً",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "مكان الإتلاف", "required": True},
                    "material_type": {"type": "text", "label": "نوع المواد", "required": True},
                    "quantity": {"type": "text", "label": "الكمية", "required": False},
                    "disposal_method": {"type": "select", "label": "طريقة الإتلاف", "options": ["حرق", "دفن", "معالجة كيميائية", "أخرى"], "required": False}
                })
            }
        ]
        
        # إضافة الانتهاكات
        added_count = 0
        for viol_data in remaining_violations:
            category_name = viol_data.pop("category")
            category_id = category_map.get(category_name)
            
            if category_id:
                existing = db.query(ViolationType).filter(
                    ViolationType.name == viol_data["name"]
                ).first()
                
                if not existing:
                    violation = ViolationType(
                        category_id=category_id,
                        **viol_data
                    )
                    db.add(violation)
                    added_count += 1
        
        db.commit()
        
        # طباعة النتائج
        total_violations = db.query(ViolationType).count()
        print(f"\n✅ تم إضافة {added_count} انتهاك جديد!")
        print(f"🏷️ إجمالي أنواع الانتهاكات: {total_violations}")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الانتهاكات: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 بدء إضافة باقي الانتهاكات...")
    add_remaining_violations()
