{% extends "base.html" %}

{% block title %}إدارة الانتهاكات - الرصد الإعلامي{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <h2 class="card-title">
                    <i class="fas fa-cogs me-2"></i>
                    إدارة الانتهاكات
                </h2>
                <p class="card-text">تصنيف الأخبار حسب ملفات حقوق الإنسان مع إدخال البيانات التفصيلية</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="row">
    <!-- قائمة الأخبار غير المصنفة -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    الأخبار غير المصنفة
                </h5>
            </div>
            <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                {% if unclassified_news %}
                    {% for article in unclassified_news %}
                    <div class="card mb-3 news-item" data-article-id="{{ article.id }}">
                        <div class="card-body">
                            <h6 class="card-title">{{ article.title[:100] }}...</h6>
                            <p class="card-text small text-muted">
                                {{ article.content[:150] }}...
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ article.created_at.strftime('%Y-%m-%d') }}
                                </small>
                                <button class="btn btn-primary btn-sm" onclick="selectArticle({{ article.id }}, '{{ article.title }}')">
                                    <i class="fas fa-tag me-1"></i>
                                    تصنيف
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5>تم تصنيف جميع الأخبار</h5>
                        <p class="text-muted">لا توجد أخبار تحتاج إلى تصنيف</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- نموذج التصنيف -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    نموذج تصنيف الانتهاك
                </h5>
            </div>
            <div class="card-body">
                <form id="violationForm">
                    <!-- معلومات المقال المحدد -->
                    <div id="selectedArticle" class="alert alert-info" style="display: none;">
                        <h6>المقال المحدد:</h6>
                        <p id="articleTitle" class="mb-0"></p>
                        <input type="hidden" id="articleId" name="news_article_id">
                    </div>

                    <!-- اختيار ملف حقوق الإنسان -->
                    <div class="mb-3">
                        <label for="humanRightsCategory" class="form-label">
                            <i class="fas fa-folder me-1"></i>
                            ملف حقوق الإنسان
                        </label>
                        <select class="form-select" id="humanRightsCategory" required>
                            <option value="">اختر ملف حقوق الإنسان</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" data-color="{{ category.color }}">
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- اختيار نوع الانتهاك -->
                    <div class="mb-3">
                        <label for="violationType" class="form-label">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            نوع الانتهاك
                        </label>
                        <select class="form-select" id="violationType" name="violation_type_id" required disabled>
                            <option value="">اختر نوع الانتهاك</option>
                        </select>
                    </div>

                    <!-- مستوى الخطورة -->
                    <div class="mb-3">
                        <label for="severityLevel" class="form-label">
                            <i class="fas fa-thermometer-half me-1"></i>
                            مستوى الخطورة
                        </label>
                        <select class="form-select" id="severityLevel" name="severity_level" required>
                            <option value="">اختر مستوى الخطورة</option>
                            <option value="منخفض">منخفض</option>
                            <option value="متوسط">متوسط</option>
                            <option value="عالي">عالي</option>
                            <option value="حرج">حرج</option>
                        </select>
                    </div>

                    <!-- الحقول الديناميكية -->
                    <div id="dynamicFields"></div>

                    <!-- أزرار التحكم -->
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-success" id="saveBtn" disabled>
                            <i class="fas fa-save me-2"></i>
                            حفظ التصنيف
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="fas fa-undo me-2"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    ملفات حقوق الإنسان
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for category in categories %}
                    <div class="col-md-4 mb-3">
                        <div class="card border-0" style="border-right: 4px solid {{ category.color }} !important;">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <i class="{{ category.icon }} fa-2x me-3" style="color: {{ category.color }};"></i>
                                    <div>
                                        <h6 class="card-title mb-1">{{ category.name }}</h6>
                                        <p class="card-text small text-muted">{{ category.description }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let selectedArticleId = null;
let currentViolationTypes = [];

// اختيار مقال للتصنيف
function selectArticle(articleId, articleTitle) {
    selectedArticleId = articleId;
    document.getElementById('articleId').value = articleId;
    document.getElementById('articleTitle').textContent = articleTitle;
    document.getElementById('selectedArticle').style.display = 'block';
    document.getElementById('saveBtn').disabled = false;
    
    // تمييز المقال المحدد
    document.querySelectorAll('.news-item').forEach(item => {
        item.classList.remove('border-primary');
    });
    document.querySelector(`[data-article-id="${articleId}"]`).classList.add('border-primary');
}

// تحميل أنواع الانتهاكات عند اختيار الفئة
document.getElementById('humanRightsCategory').addEventListener('change', async function() {
    const categoryId = this.value;
    const violationTypeSelect = document.getElementById('violationType');
    
    if (!categoryId) {
        violationTypeSelect.disabled = true;
        violationTypeSelect.innerHTML = '<option value="">اختر نوع الانتهاك</option>';
        return;
    }
    
    try {
        const response = await fetch(`/api/violation-types/${categoryId}`);
        const violationTypes = await response.json();
        
        violationTypeSelect.innerHTML = '<option value="">اختر نوع الانتهاك</option>';
        violationTypes.forEach(vt => {
            const option = document.createElement('option');
            option.value = vt.id;
            option.textContent = vt.name;
            option.dataset.requiredFields = JSON.stringify(vt.required_fields);
            violationTypeSelect.appendChild(option);
        });
        
        violationTypeSelect.disabled = false;
        currentViolationTypes = violationTypes;
        
    } catch (error) {
        console.error('خطأ في تحميل أنواع الانتهاكات:', error);
        alert('حدث خطأ في تحميل أنواع الانتهاكات');
    }
});

// إنشاء الحقول الديناميكية عند اختيار نوع الانتهاك
document.getElementById('violationType').addEventListener('change', function() {
    const selectedOption = this.options[this.selectedIndex];
    const requiredFields = selectedOption.dataset.requiredFields;
    
    if (!requiredFields) {
        document.getElementById('dynamicFields').innerHTML = '';
        return;
    }
    
    try {
        const fields = JSON.parse(requiredFields);
        generateDynamicFields(fields);
    } catch (error) {
        console.error('خطأ في تحليل الحقول المطلوبة:', error);
    }
});

// إنشاء الحقول الديناميكية
function generateDynamicFields(fields) {
    const container = document.getElementById('dynamicFields');
    container.innerHTML = '<hr><h6 class="text-primary">بيانات تفصيلية:</h6>';
    
    Object.keys(fields).forEach(fieldName => {
        const field = fields[fieldName];
        const fieldDiv = document.createElement('div');
        fieldDiv.className = 'mb-3';
        
        let fieldHtml = `<label for="${fieldName}" class="form-label">${field.label}</label>`;
        
        switch (field.type) {
            case 'text':
                fieldHtml += `<input type="text" class="form-control" id="${fieldName}" name="${fieldName}" ${field.required ? 'required' : ''}>`;
                break;
            case 'number':
                fieldHtml += `<input type="number" class="form-control" id="${fieldName}" name="${fieldName}" ${field.required ? 'required' : ''}>`;
                break;
            case 'date':
                fieldHtml += `<input type="date" class="form-control" id="${fieldName}" name="${fieldName}" ${field.required ? 'required' : ''}>`;
                break;
            case 'textarea':
                fieldHtml += `<textarea class="form-control" id="${fieldName}" name="${fieldName}" rows="3" ${field.required ? 'required' : ''}></textarea>`;
                break;
            case 'select':
                fieldHtml += `<select class="form-select" id="${fieldName}" name="${fieldName}" ${field.required ? 'required' : ''}>`;
                fieldHtml += '<option value="">اختر...</option>';
                field.options.forEach(option => {
                    fieldHtml += `<option value="${option}">${option}</option>`;
                });
                fieldHtml += '</select>';
                break;
        }
        
        fieldDiv.innerHTML = fieldHtml;
        container.appendChild(fieldDiv);
    });
}

// حفظ التصنيف
document.getElementById('violationForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (!selectedArticleId) {
        alert('يرجى اختيار مقال للتصنيف');
        return;
    }
    
    const formData = new FormData(this);
    
    // جمع البيانات الإضافية من الحقول الديناميكية
    const additionalData = {};
    const dynamicFields = document.querySelectorAll('#dynamicFields input, #dynamicFields select, #dynamicFields textarea');
    dynamicFields.forEach(field => {
        if (field.name && field.value) {
            additionalData[field.name] = field.value;
        }
    });
    
    formData.append('additional_data', JSON.stringify(additionalData));
    
    try {
        const response = await fetch('/api/violation-data', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('تم حفظ التصنيف بنجاح!');
            location.reload();
        } else {
            alert('خطأ: ' + result.message);
        }
    } catch (error) {
        console.error('خطأ في حفظ التصنيف:', error);
        alert('حدث خطأ في حفظ التصنيف');
    }
});

// إعادة تعيين النموذج
function resetForm() {
    document.getElementById('violationForm').reset();
    document.getElementById('selectedArticle').style.display = 'none';
    document.getElementById('dynamicFields').innerHTML = '';
    document.getElementById('violationType').disabled = true;
    document.getElementById('saveBtn').disabled = true;
    selectedArticleId = null;
    
    document.querySelectorAll('.news-item').forEach(item => {
        item.classList.remove('border-primary');
    });
}
</script>
{% endblock %}
