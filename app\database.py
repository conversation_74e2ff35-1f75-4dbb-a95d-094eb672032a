from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os

# إعداد قاعدة البيانات
SQLALCHEMY_DATABASE_URL = "sqlite:///./violations_news.db"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL, 
    connect_args={"check_same_thread": False}
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# نموذج الأخبار
class NewsArticle(Base):
    __tablename__ = "news_articles"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, index=True)
    content = Column(Text)
    source = Column(String)
    url = Column(String, unique=True)
    published_date = Column(DateTime, default=datetime.utcnow)
    category = Column(String, default="عام")
    is_violation = Column(Boolean, default=False)
    severity = Column(String, default="منخفض")  # منخفض، متوسط، عالي
    created_at = Column(DateTime, default=datetime.utcnow)

# نموذج المصادر الإعلامية
class MediaSource(Base):
    __tablename__ = "media_sources"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    url = Column(String)
    rss_feed = Column(String)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

# إنشاء الجداول
def create_tables():
    Base.metadata.create_all(bind=engine)

# الحصول على جلسة قاعدة البيانات
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
