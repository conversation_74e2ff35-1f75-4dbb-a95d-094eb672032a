{% extends "base.html" %}

{% block title %}الأخبار - الرصد الإعلامي{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title">
                    <i class="fas fa-newspaper me-2"></i>
                    جميع الأخبار
                </h2>
                <p class="card-text text-muted">تصفح وابحث في جميع الأخبار المرصودة</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    فلترة النتائج
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" action="/news">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="category" class="form-label">الفئة</label>
                            <select class="form-select" name="category" id="category">
                                <option value="">جميع الفئات</option>
                                {% for category in categories %}
                                <option value="{{ category }}" {% if current_category == category %}selected{% endif %}>
                                    {{ category }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <label for="source" class="form-label">المصدر</label>
                            <select class="form-select" name="source" id="source">
                                <option value="">جميع المصادر</option>
                                {% for source in sources %}
                                <option value="{{ source }}" {% if current_source == source %}selected{% endif %}>
                                    {{ source }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" name="search" id="search" 
                                   placeholder="ابحث في العناوين..." value="{{ current_search or '' }}">
                        </div>
                        
                        <div class="col-md-2 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- News List -->
<div class="row">
    <div class="col-12">
        {% if news %}
            {% for article in news %}
            <div class="card mb-3 {% if article.is_violation %}violation-item{% else %}news-item{% endif %}">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-9">
                            <h5 class="card-title">
                                {{ article.title }}
                                {% if article.is_violation %}
                                <span class="badge violation-badge me-2">انتهاك</span>
                                {% endif %}
                                <span class="badge bg-secondary">{{ article.category }}</span>
                            </h5>
                            <p class="card-text">
                                {{ article.content[:300] }}...
                            </p>
                            <div class="d-flex flex-wrap gap-2 mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-source me-1"></i>
                                    {{ article.source }}
                                </small>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ article.created_at.strftime('%Y-%m-%d %H:%M') }}
                                </small>
                                {% if article.is_violation %}
                                <small class="text-danger">
                                    <i class="fas fa-exclamation-circle me-1"></i>
                                    مستوى الخطورة: {{ article.severity }}
                                </small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-3 text-end">
                            <div class="d-grid gap-2">
                                <a href="{{ article.url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-external-link-alt me-1"></i>
                                    قراءة المقال
                                </a>
                                {% if not article.is_violation %}
                                <button class="btn btn-outline-warning btn-sm" onclick="markAsViolation({{ article.id }})">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    تحديد كانتهاك
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-search fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد نتائج</h4>
                        <p class="text-muted">لم يتم العثور على أخبار تطابق معايير البحث</p>
                        <a href="/news" class="btn btn-primary">
                            <i class="fas fa-refresh me-1"></i>
                            إعادة تعيين الفلاتر
                        </a>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Pagination (يمكن إضافتها لاحقاً) -->
<div class="row mt-4">
    <div class="col-12 text-center">
        <p class="text-muted">عرض {{ news|length }} من الأخبار</p>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
async function markAsViolation(articleId) {
    if (!confirm('هل أنت متأكد من تحديد هذا الخبر كانتهاك؟')) {
        return;
    }
    
    try {
        // هنا يمكن إضافة API call لتحديث حالة المقال
        alert('تم تحديد المقال كانتهاك بنجاح');
        location.reload();
    } catch (error) {
        alert('حدث خطأ أثناء تحديث المقال');
    }
}

// تحسين تجربة المستخدم للفلاتر
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('select, input');
    
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            // يمكن إضافة تحديث تلقائي للنتائج هنا
        });
    });
});
</script>
{% endblock %}
