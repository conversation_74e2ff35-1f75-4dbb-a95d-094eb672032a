{% extends "base.html" %}

{% block title %}التصنيفات - الرصد الإعلامي{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <h2 class="card-title">
                    <i class="fas fa-tags me-2"></i>
                    التصنيفات الإعلامية
                </h2>
                <p class="card-text">تصنيف وتحليل المحتوى الإعلامي حسب الأهمية والموضوع</p>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <i class="fas fa-tags fa-3x text-primary mb-3"></i>
                <h3 class="card-title text-primary">{{ classifications|length }}</h3>
                <p class="card-text">إجمالي التصنيفات</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <i class="fas fa-chart-line fa-3x text-warning mb-3"></i>
                <h3 class="card-title text-warning">
                    {{ classifications|selectattr("classification_level", "equalto", "عالي")|list|length }}
                </h3>
                <p class="card-text">تصنيفات عالية الأهمية</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <i class="fas fa-calendar-week fa-3x text-info mb-3"></i>
                <h3 class="card-title text-info">
                    {{ classifications|selectattr("created_at")|list|length }}
                </h3>
                <p class="card-text">تصنيفات هذا الأسبوع</p>
            </div>
        </div>
    </div>
</div>

<!-- Level Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="btn-group" role="group" aria-label="فلترة حسب الأهمية">
                    <button type="button" class="btn btn-outline-secondary active" onclick="filterByLevel('all')">
                        جميع التصنيفات
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="filterByLevel('عالي')">
                        عالي الأهمية
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="filterByLevel('متوسط')">
                        متوسط الأهمية
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="filterByLevel('منخفض')">
                        منخفض الأهمية
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Classifications List -->
<div class="row">
    <div class="col-12">
        {% if classifications %}
            {% for classification in classifications %}
            <div class="card mb-3 classification-item" data-level="{{ classification.classification_level }}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <span class="badge bg-primary me-2">مصنف</span>
                        <span class="badge 
                            {% if classification.classification_level == 'عالي' %}bg-danger
                            {% elif classification.classification_level == 'متوسط' %}bg-warning
                            {% else %}bg-info{% endif %}">
                            {{ classification.classification_level }}
                        </span>
                        <small class="text-muted ms-2">
                            <i class="fas fa-calendar me-1"></i>
                            {{ classification.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </small>
                    </div>
                    <div>
                        <small class="text-muted">
                            <i class="fas fa-source me-1"></i>
                            {{ classification.source }}
                        </small>
                    </div>
                </div>
                <div class="card-body">
                    <h5 class="card-title text-primary">
                        <i class="fas fa-tag me-2"></i>
                        {{ classification.title }}
                    </h5>
                    <p class="card-text">
                        {{ classification.content[:400] }}...
                    </p>
                    
                    <!-- Classification Details -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="alert alert-primary" role="alert">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-1"></i>
                                    تفاصيل التصنيف
                                </h6>
                                <hr>
                                <p class="mb-0">
                                    <strong>نوع التصنيف:</strong> {{ classification.category }}<br>
                                    <strong>مستوى الأهمية:</strong> {{ classification.classification_level }}<br>
                                    <strong>تاريخ التصنيف:</strong> {{ classification.created_at.strftime('%Y-%m-%d') }}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-grid gap-2">
                                <a href="{{ classification.url }}" target="_blank" class="btn btn-outline-primary">
                                    <i class="fas fa-external-link-alt me-1"></i>
                                    عرض المصدر الأصلي
                                </a>
                                <button class="btn btn-outline-warning" onclick="generateReport('{{ classification.id }}')">
                                    <i class="fas fa-file-alt me-1"></i>
                                    إنشاء تقرير
                                </button>
                                <button class="btn btn-outline-success" onclick="markAsProcessed('{{ classification.id }}')">
                                    <i class="fas fa-check me-1"></i>
                                    تم المعالجة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-tags fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد تصنيفات</h4>
                        <p class="text-muted">لم يتم تصنيف أي محتوى إعلامي حتى الآن</p>
                        <a href="/news" class="btn btn-primary">
                            <i class="fas fa-newspaper me-1"></i>
                            عرض جميع الأخبار
                        </a>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Export Options -->
{% if classifications %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-download me-2"></i>
                    تصدير البيانات
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-success w-100" onclick="exportToExcel()">
                            <i class="fas fa-file-excel me-2"></i>
                            تصدير إلى Excel
                        </button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-danger w-100" onclick="exportToPDF()">
                            <i class="fas fa-file-pdf me-2"></i>
                            تصدير إلى PDF
                        </button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-info w-100" onclick="generateSummaryReport()">
                            <i class="fas fa-chart-bar me-2"></i>
                            تقرير إحصائي
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
function filterByLevel(level) {
    const cards = document.querySelectorAll('[data-level]');
    const buttons = document.querySelectorAll('.btn-group button');
    
    // إزالة الحالة النشطة من جميع الأزرار
    buttons.forEach(btn => btn.classList.remove('active'));
    
    // إضافة الحالة النشطة للزر المحدد
    event.target.classList.add('active');
    
    cards.forEach(card => {
        if (level === 'all' || card.dataset.level === level) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

async function markAsProcessed(classificationId) {
    if (!confirm('هل أنت متأكد من تحديد هذا التصنيف كمعالج؟')) {
        return;
    }
    
    try {
        alert('تم تحديد التصنيف كمعالج بنجاح');
        location.reload();
    } catch (error) {
        alert('حدث خطأ أثناء تحديث التصنيف');
    }
}

function generateReport(classificationId) {
    alert('سيتم إنشاء تقرير مفصل للتصنيف رقم ' + classificationId);
}

function exportToExcel() {
    alert('سيتم تصدير البيانات إلى ملف Excel');
}

function exportToPDF() {
    alert('سيتم تصدير البيانات إلى ملف PDF');
}

function generateSummaryReport() {
    alert('سيتم إنشاء تقرير إحصائي شامل');
}
</script>
{% endblock %}
