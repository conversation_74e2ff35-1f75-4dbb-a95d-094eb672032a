from fastapi import FastAPI, Depends, HTTPException, Request, Form
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import feedparser
import requests
from bs4 import BeautifulSoup
from datetime import datetime, timedelta

from . import models
from .database import get_db, create_tables, NewsArticle, MediaSource

# إنشاء التطبيق
app = FastAPI(title="الرصد الإعلامي", description="نظام رصد الأخبار والانتهاكات الإعلامية")

# إعداد الملفات الثابتة والقوالب
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="app/templates")

# إنشاء قاعدة البيانات عند بدء التطبيق
@app.on_event("startup")
async def startup_event():
    create_tables()

# الصفحة الرئيسية
@app.get("/", response_class=HTMLResponse)
async def home(request: Request, db: Session = Depends(get_db)):
    # جلب آخر الأخبار
    latest_news = db.query(NewsArticle).order_by(NewsArticle.created_at.desc()).limit(10).all()
    
    # إحصائيات
    total_news = db.query(NewsArticle).count()
    classifications_count = db.query(NewsArticle).filter(NewsArticle.is_classified == True).count()
    sources_count = db.query(MediaSource).count()
    
    stats = {
        "total_news": total_news,
        "classifications_count": classifications_count,
        "sources_count": sources_count,
        "today_news": db.query(NewsArticle).filter(
            NewsArticle.created_at >= datetime.now().date()
        ).count()
    }
    
    return templates.TemplateResponse("index.html", {
        "request": request,
        "news": latest_news,
        "stats": stats
    })

# صفحة الأخبار
@app.get("/news", response_class=HTMLResponse)
async def news_page(
    request: Request,
    category: Optional[str] = None,
    source: Optional[str] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db)
):
    query = db.query(NewsArticle)
    
    # تطبيق الفلاتر
    if category:
        query = query.filter(NewsArticle.category == category)
    if source:
        query = query.filter(NewsArticle.source == source)
    if search:
        query = query.filter(NewsArticle.title.contains(search))
    
    news = query.order_by(NewsArticle.created_at.desc()).limit(50).all()
    
    # جلب الفئات والمصادر للفلترة
    categories = db.query(NewsArticle.category).distinct().all()
    sources = db.query(NewsArticle.source).distinct().all()
    
    return templates.TemplateResponse("news.html", {
        "request": request,
        "news": news,
        "categories": [c[0] for c in categories],
        "sources": [s[0] for s in sources],
        "current_category": category,
        "current_source": source,
        "current_search": search
    })

# صفحة التصنيفات
@app.get("/classifications", response_class=HTMLResponse)
async def classifications_page(request: Request, db: Session = Depends(get_db)):
    classifications = db.query(NewsArticle).filter(
        NewsArticle.is_classified == True
    ).order_by(NewsArticle.created_at.desc()).all()

    return templates.TemplateResponse("classifications.html", {
        "request": request,
        "classifications": classifications
    })

# API لإضافة خبر جديد
@app.post("/api/news", response_model=models.NewsArticle)
async def create_news(news: models.NewsArticleCreate, db: Session = Depends(get_db)):
    db_news = NewsArticle(**news.dict())
    db.add(db_news)
    db.commit()
    db.refresh(db_news)
    return db_news

# API لجلب الأخبار
@app.get("/api/news", response_model=List[models.NewsArticle])
async def get_news(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    news = db.query(NewsArticle).offset(skip).limit(limit).all()
    return news

# API لإضافة مصدر إعلامي
@app.post("/api/sources", response_model=models.MediaSource)
async def create_source(source: models.MediaSourceCreate, db: Session = Depends(get_db)):
    db_source = MediaSource(**source.dict())
    db.add(db_source)
    db.commit()
    db.refresh(db_source)
    return db_source

# وظيفة لجمع الأخبار من RSS
@app.post("/api/collect-news")
async def collect_news(db: Session = Depends(get_db)):
    sources = db.query(MediaSource).filter(MediaSource.is_active == True).all()
    collected_count = 0
    
    for source in sources:
        if source.rss_feed:
            try:
                feed = feedparser.parse(source.rss_feed)
                for entry in feed.entries[:10]:  # آخر 10 أخبار
                    # التحقق من عدم وجود الخبر مسبقاً
                    existing = db.query(NewsArticle).filter(
                        NewsArticle.url == entry.link
                    ).first()
                    
                    if not existing:
                        news_article = NewsArticle(
                            title=entry.title,
                            content=entry.summary if hasattr(entry, 'summary') else entry.title,
                            source=source.name,
                            url=entry.link,
                            published_date=datetime(*entry.published_parsed[:6]) if hasattr(entry, 'published_parsed') else datetime.now()
                        )
                        db.add(news_article)
                        collected_count += 1
                
                db.commit()
            except Exception as e:
                print(f"خطأ في جمع الأخبار من {source.name}: {e}")
    
    return {"message": f"تم جمع {collected_count} خبر جديد"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
