from fastapi import FastAPI, Depends, HTTPException, Request, Form
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import feedparser
import requests
from bs4 import BeautifulSoup
from datetime import datetime, timedelta

from . import models
from .database import get_db, create_tables, NewsArticle, MediaSource, HumanRightsCategory, ViolationType, ViolationData
import json

# إنشاء التطبيق
app = FastAPI(title="الرصد الإعلامي", description="نظام رصد الأخبار والانتهاكات الإعلامية")

# إعداد الملفات الثابتة والقوالب
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="app/templates")

# إنشاء قاعدة البيانات عند بدء التطبيق
@app.on_event("startup")
async def startup_event():
    create_tables()

# الصفحة الرئيسية
@app.get("/", response_class=HTMLResponse)
async def home(request: Request, db: Session = Depends(get_db)):
    # جلب آخر الأخبار
    latest_news = db.query(NewsArticle).order_by(NewsArticle.created_at.desc()).limit(10).all()
    
    # إحصائيات
    total_news = db.query(NewsArticle).count()
    classifications_count = db.query(NewsArticle).filter(NewsArticle.is_classified == True).count()
    sources_count = db.query(MediaSource).count()
    
    stats = {
        "total_news": total_news,
        "classifications_count": classifications_count,
        "sources_count": sources_count,
        "today_news": db.query(NewsArticle).filter(
            NewsArticle.created_at >= datetime.now().date()
        ).count()
    }
    
    return templates.TemplateResponse("index.html", {
        "request": request,
        "news": latest_news,
        "stats": stats
    })

# صفحة الأخبار
@app.get("/news", response_class=HTMLResponse)
async def news_page(
    request: Request,
    category: Optional[str] = None,
    source: Optional[str] = None,
    search: Optional[str] = None,
    db: Session = Depends(get_db)
):
    query = db.query(NewsArticle)
    
    # تطبيق الفلاتر
    if category:
        query = query.filter(NewsArticle.category == category)
    if source:
        query = query.filter(NewsArticle.source == source)
    if search:
        query = query.filter(NewsArticle.title.contains(search))
    
    news = query.order_by(NewsArticle.created_at.desc()).limit(50).all()
    
    # جلب الفئات والمصادر للفلترة
    categories = db.query(NewsArticle.category).distinct().all()
    sources = db.query(NewsArticle.source).distinct().all()
    
    return templates.TemplateResponse("news.html", {
        "request": request,
        "news": news,
        "categories": [c[0] for c in categories],
        "sources": [s[0] for s in sources],
        "current_category": category,
        "current_source": source,
        "current_search": search
    })

# صفحة التصنيفات
@app.get("/classifications", response_class=HTMLResponse)
async def classifications_page(request: Request, db: Session = Depends(get_db)):
    classifications = db.query(NewsArticle).filter(
        NewsArticle.is_classified == True
    ).order_by(NewsArticle.created_at.desc()).all()

    return templates.TemplateResponse("classifications.html", {
        "request": request,
        "classifications": classifications
    })

# API لإضافة خبر جديد
@app.post("/api/news", response_model=models.NewsArticle)
async def create_news(news: models.NewsArticleCreate, db: Session = Depends(get_db)):
    db_news = NewsArticle(**news.dict())
    db.add(db_news)
    db.commit()
    db.refresh(db_news)
    return db_news

# API لجلب الأخبار
@app.get("/api/news", response_model=List[models.NewsArticle])
async def get_news(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    news = db.query(NewsArticle).offset(skip).limit(limit).all()
    return news

# API لإضافة مصدر إعلامي
@app.post("/api/sources", response_model=models.MediaSource)
async def create_source(source: models.MediaSourceCreate, db: Session = Depends(get_db)):
    db_source = MediaSource(**source.dict())
    db.add(db_source)
    db.commit()
    db.refresh(db_source)
    return db_source

# وظيفة لجمع الأخبار من RSS
@app.post("/api/collect-news")
async def collect_news(db: Session = Depends(get_db)):
    sources = db.query(MediaSource).filter(MediaSource.is_active == True).all()
    collected_count = 0
    
    for source in sources:
        if source.rss_feed:
            try:
                feed = feedparser.parse(source.rss_feed)
                for entry in feed.entries[:10]:  # آخر 10 أخبار
                    # التحقق من عدم وجود الخبر مسبقاً
                    existing = db.query(NewsArticle).filter(
                        NewsArticle.url == entry.link
                    ).first()
                    
                    if not existing:
                        news_article = NewsArticle(
                            title=entry.title,
                            content=entry.summary if hasattr(entry, 'summary') else entry.title,
                            source=source.name,
                            url=entry.link,
                            published_date=datetime(*entry.published_parsed[:6]) if hasattr(entry, 'published_parsed') else datetime.now()
                        )
                        db.add(news_article)
                        collected_count += 1
                
                db.commit()
            except Exception as e:
                print(f"خطأ في جمع الأخبار من {source.name}: {e}")
    
    return {"message": f"تم جمع {collected_count} خبر جديد"}

# صفحة إدارة الانتهاكات
@app.get("/violations-management", response_class=HTMLResponse)
async def violations_management(request: Request, db: Session = Depends(get_db)):
    # جلب ملفات حقوق الإنسان
    categories = db.query(HumanRightsCategory).filter(HumanRightsCategory.is_active == True).all()

    # جلب أنواع الانتهاكات
    violation_types = db.query(ViolationType).filter(ViolationType.is_active == True).all()

    # جلب الأخبار غير المصنفة
    unclassified_news = db.query(NewsArticle).filter(NewsArticle.is_classified == False).limit(20).all()

    return templates.TemplateResponse("violations_management.html", {
        "request": request,
        "categories": categories,
        "violation_types": violation_types,
        "unclassified_news": unclassified_news
    })

# API لجلب أنواع الانتهاكات حسب الفئة
@app.get("/api/violation-types/{category_id}")
async def get_violation_types(category_id: int, db: Session = Depends(get_db)):
    violation_types = db.query(ViolationType).filter(
        ViolationType.category_id == category_id,
        ViolationType.is_active == True
    ).all()

    return [
        {
            "id": vt.id,
            "name": vt.name,
            "description": vt.description,
            "required_fields": json.loads(vt.required_fields) if vt.required_fields else {}
        }
        for vt in violation_types
    ]

# API لحفظ بيانات الانتهاك
@app.post("/api/violation-data")
async def save_violation_data(
    news_article_id: int = Form(...),
    violation_type_id: int = Form(...),
    location: str = Form(None),
    date_occurred: str = Form(None),
    victims_count: int = Form(None),
    severity_level: str = Form(...),
    additional_data: str = Form("{}"),
    db: Session = Depends(get_db)
):
    try:
        # تحديث المقال كمصنف
        article = db.query(NewsArticle).filter(NewsArticle.id == news_article_id).first()
        if article:
            article.is_classified = True
            article.classification_level = severity_level

        # إنشاء بيانات الانتهاك
        violation_data = ViolationData(
            news_article_id=news_article_id,
            violation_type_id=violation_type_id,
            location=location,
            date_occurred=datetime.fromisoformat(date_occurred) if date_occurred else None,
            victims_count=victims_count,
            severity_level=severity_level,
            additional_data=additional_data
        )

        db.add(violation_data)
        db.commit()

        return {"success": True, "message": "تم حفظ بيانات الانتهاك بنجاح"}

    except Exception as e:
        db.rollback()
        return {"success": False, "message": f"خطأ في حفظ البيانات: {str(e)}"}

# صفحة تقارير الانتهاكات
@app.get("/violations-reports", response_class=HTMLResponse)
async def violations_reports(request: Request, db: Session = Depends(get_db)):
    # إحصائيات الانتهاكات
    total_violations = db.query(ViolationData).count()

    # الانتهاكات حسب الفئة
    categories_stats = db.query(HumanRightsCategory).all()

    # الانتهاكات حسب مستوى الخطورة
    severity_stats = {}
    for level in ["منخفض", "متوسط", "عالي", "حرج"]:
        count = db.query(ViolationData).filter(ViolationData.severity_level == level).count()
        severity_stats[level] = count

    return templates.TemplateResponse("violations_reports.html", {
        "request": request,
        "total_violations": total_violations,
        "categories_stats": categories_stats,
        "severity_stats": severity_stats
    })

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
