// الرصد الإعلامي - ملف JavaScript الرئيسي

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// تهيئة التطبيق
function initializeApp() {
    // تهيئة التلميحات
    initializeTooltips();
    
    // تهيئة التأثيرات
    initializeAnimations();
    
    // تهيئة البحث المباشر
    initializeLiveSearch();
    
    // تهيئة الإشعارات
    initializeNotifications();
    
    console.log('تم تهيئة تطبيق الرصد الإعلامي بنجاح');
}

// تهيئة التلميحات
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// تهيئة التأثيرات
function initializeAnimations() {
    // تأثير الظهور التدريجي للبطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// تهيئة البحث المباشر
function initializeLiveSearch() {
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performLiveSearch(this.value);
            }, 500);
        });
    }
}

// تنفيذ البحث المباشر
function performLiveSearch(query) {
    if (query.length < 3) return;
    
    // إضافة مؤشر التحميل
    showLoadingIndicator();
    
    // محاكاة استدعاء API
    setTimeout(() => {
        hideLoadingIndicator();
        console.log('البحث عن:', query);
    }, 1000);
}

// عرض مؤشر التحميل
function showLoadingIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'loading-indicator';
    indicator.className = 'position-fixed top-50 start-50 translate-middle';
    indicator.innerHTML = `
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
    `;
    document.body.appendChild(indicator);
}

// إخفاء مؤشر التحميل
function hideLoadingIndicator() {
    const indicator = document.getElementById('loading-indicator');
    if (indicator) {
        indicator.remove();
    }
}

// تهيئة الإشعارات
function initializeNotifications() {
    // التحقق من دعم الإشعارات
    if ('Notification' in window) {
        if (Notification.permission === 'default') {
            Notification.requestPermission();
        }
    }
}

// عرض إشعار
function showNotification(title, message, type = 'info') {
    // إشعار المتصفح
    if (Notification.permission === 'granted') {
        new Notification(title, {
            body: message,
            icon: '/static/images/logo.png'
        });
    }
    
    // إشعار داخل الصفحة
    showToast(title, message, type);
}

// عرض رسالة منبثقة
function showToast(title, message, type = 'info') {
    const toastContainer = getOrCreateToastContainer();
    
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${title}</strong><br>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement);
    toast.show();
    
    // إزالة التوست بعد إخفائه
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}

// الحصول على أو إنشاء حاوية الرسائل المنبثقة
function getOrCreateToastContainer() {
    let container = document.getElementById('toast-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(container);
    }
    return container;
}

// وظائف API

// جمع الأخبار الجديدة
async function collectNews() {
    const button = event.target;
    const originalText = button.innerHTML;
    
    try {
        // تعطيل الزر وإظهار مؤشر التحميل
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري جمع الأخبار...';
        
        const response = await fetch('/api/collect-news', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (response.ok) {
            showNotification('نجح جمع الأخبار', result.message, 'success');
            setTimeout(() => location.reload(), 2000);
        } else {
            throw new Error(result.detail || 'حدث خطأ غير معروف');
        }
    } catch (error) {
        console.error('خطأ في جمع الأخبار:', error);
        showNotification('خطأ', 'حدث خطأ أثناء جمع الأخبار: ' + error.message, 'danger');
    } finally {
        button.disabled = false;
        button.innerHTML = originalText;
    }
}

// تصنيف مقال
async function markAsClassified(articleId, level = 'متوسط') {
    if (!confirm('هل أنت متأكد من تصنيف هذا المقال؟')) {
        return;
    }

    try {
        const response = await fetch(`/api/news/${articleId}/mark-classified`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                is_classified: true,
                classification_level: level
            })
        });

        if (response.ok) {
            showNotification('تم التصنيف', 'تم تصنيف المقال بنجاح', 'primary');
            setTimeout(() => location.reload(), 1500);
        } else {
            throw new Error('فشل في تصنيف المقال');
        }
    } catch (error) {
        console.error('خطأ في تصنيف المقال:', error);
        showNotification('خطأ', 'حدث خطأ أثناء تصنيف المقال', 'danger');
    }
}

// تصدير البيانات
function exportData(format) {
    showNotification('جاري التصدير', `سيتم تصدير البيانات بصيغة ${format}`, 'info');
    
    // محاكاة عملية التصدير
    setTimeout(() => {
        showNotification('تم التصدير', `تم تصدير البيانات بصيغة ${format} بنجاح`, 'success');
    }, 2000);
}

// فلترة النتائج
function filterResults(filterType, filterValue) {
    const items = document.querySelectorAll('[data-' + filterType + ']');

    items.forEach(item => {
        const itemValue = item.getAttribute('data-' + filterType);
        if (filterValue === 'all' || itemValue === filterValue) {
            item.style.display = 'block';
            item.style.animation = 'fadeIn 0.5s ease';
        } else {
            item.style.display = 'none';
        }
    });

    // تحديث عداد النتائج
    updateResultsCounter();
}

// فلترة حسب مستوى التصنيف
function filterByLevel(level) {
    const cards = document.querySelectorAll('[data-level]');
    const buttons = document.querySelectorAll('.btn-group button');

    // إزالة الحالة النشطة من جميع الأزرار
    buttons.forEach(btn => btn.classList.remove('active'));

    // إضافة الحالة النشطة للزر المحدد
    event.target.classList.add('active');

    cards.forEach(card => {
        if (level === 'all' || card.dataset.level === level) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// تحديث عداد النتائج
function updateResultsCounter() {
    const visibleItems = document.querySelectorAll('[data-level]:not([style*="display: none"])');
    const counter = document.getElementById('results-counter');

    if (counter) {
        counter.textContent = `عرض ${visibleItems.length} من النتائج`;
    }
}

// تحديث الوقت الحقيقي
function updateRealTime() {
    const timeElements = document.querySelectorAll('.real-time');
    
    timeElements.forEach(element => {
        const timestamp = element.getAttribute('data-timestamp');
        if (timestamp) {
            const date = new Date(timestamp);
            const now = new Date();
            const diff = now - date;
            
            // تحويل الفرق إلى نص مفهوم
            const timeAgo = formatTimeAgo(diff);
            element.textContent = timeAgo;
        }
    });
}

// تنسيق الوقت المنقضي
function formatTimeAgo(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `منذ ${days} يوم`;
    if (hours > 0) return `منذ ${hours} ساعة`;
    if (minutes > 0) return `منذ ${minutes} دقيقة`;
    return 'منذ لحظات';
}

// تحديث الوقت كل دقيقة
setInterval(updateRealTime, 60000);

// وظائف مساعدة

// التحقق من الاتصال بالإنترنت
function checkConnection() {
    return navigator.onLine;
}

// حفظ البيانات محلياً
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (error) {
        console.error('خطأ في حفظ البيانات محلياً:', error);
        return false;
    }
}

// استرجاع البيانات المحفوظة محلياً
function getFromLocalStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (error) {
        console.error('خطأ في استرجاع البيانات المحفوظة:', error);
        return null;
    }
}

// تنظيف البيانات المحفوظة محلياً
function clearLocalStorage() {
    try {
        localStorage.clear();
        showNotification('تم التنظيف', 'تم مسح البيانات المحفوظة محلياً', 'info');
    } catch (error) {
        console.error('خطأ في تنظيف البيانات:', error);
    }
}

// إضافة CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .fade-in {
        animation: fadeIn 0.5s ease;
    }
`;
document.head.appendChild(style);
