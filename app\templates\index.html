{% extends "base.html" %}

{% block title %}الرئيسية - الرصد الإعلامي{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card stats-card">
            <div class="card-body text-center py-5">
                <h1 class="display-4 mb-3">
                    <i class="fas fa-satellite-dish me-3"></i>
                    الرصد الإعلامي
                </h1>
                <p class="lead">نظام متطور لرصد وتحليل الأخبار والانتهاكات الإعلامية</p>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-newspaper fa-3x text-primary mb-3"></i>
                <h3 class="card-title">{{ stats.total_news }}</h3>
                <p class="card-text">إجمالي الأخبار</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                <h3 class="card-title">{{ stats.violations_count }}</h3>
                <p class="card-text">الانتهاكات</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-rss fa-3x text-success mb-3"></i>
                <h3 class="card-title">{{ stats.sources_count }}</h3>
                <p class="card-text">المصادر الإعلامية</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-calendar-day fa-3x text-info mb-3"></i>
                <h3 class="card-title">{{ stats.today_news }}</h3>
                <p class="card-text">أخبار اليوم</p>
            </div>
        </div>
    </div>
</div>

<!-- Latest News Section -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    آخر الأخبار
                </h4>
                <a href="/news" class="btn btn-primary btn-sm">
                    عرض جميع الأخبار
                    <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
            <div class="card-body">
                {% if news %}
                    {% for article in news %}
                    <div class="card mb-3 {% if article.is_violation %}violation-item{% else %}news-item{% endif %}">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h5 class="card-title">
                                        {{ article.title }}
                                        {% if article.is_violation %}
                                        <span class="badge violation-badge me-2">انتهاك</span>
                                        {% endif %}
                                    </h5>
                                    <p class="card-text text-muted">
                                        {{ article.content[:200] }}...
                                    </p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <small class="text-muted d-block">
                                        <i class="fas fa-source me-1"></i>
                                        {{ article.source }}
                                    </small>
                                    <small class="text-muted d-block">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ article.created_at.strftime('%Y-%m-%d %H:%M') }}
                                    </small>
                                    <small class="text-muted d-block">
                                        <i class="fas fa-tag me-1"></i>
                                        {{ article.category }}
                                    </small>
                                    {% if article.is_violation %}
                                    <small class="text-danger d-block">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ article.severity }}
                                    </small>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <a href="{{ article.url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-external-link-alt me-1"></i>
                                        قراءة المقال كاملاً
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-newspaper fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد أخبار حالياً</h4>
                        <p class="text-muted">سيتم عرض الأخبار هنا عند إضافتها</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    إجراءات سريعة
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-success w-100" onclick="collectNews()">
                            <i class="fas fa-download me-2"></i>
                            جمع الأخبار الجديدة
                        </button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="/violations" class="btn btn-warning w-100">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            عرض الانتهاكات
                        </a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="/news" class="btn btn-info w-100">
                            <i class="fas fa-search me-2"></i>
                            البحث في الأخبار
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
async function collectNews() {
    const button = event.target;
    const originalText = button.innerHTML;
    
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري جمع الأخبار...';
    
    try {
        const response = await fetch('/api/collect-news', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const result = await response.json();
        
        if (response.ok) {
            alert(result.message);
            location.reload();
        } else {
            alert('حدث خطأ أثناء جمع الأخبار');
        }
    } catch (error) {
        alert('حدث خطأ في الاتصال');
    } finally {
        button.disabled = false;
        button.innerHTML = originalText;
    }
}
</script>
{% endblock %}
