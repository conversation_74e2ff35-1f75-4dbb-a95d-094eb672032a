{% extends "base.html" %}

{% block title %}الانتهاكات - الرصد الإعلامي{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <h2 class="card-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    الانتهاكات الإعلامية
                </h2>
                <p class="card-text">رصد وتتبع الانتهاكات والمخالفات في المحتوى الإعلامي</p>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <i class="fas fa-exclamation-circle fa-3x text-danger mb-3"></i>
                <h3 class="card-title text-danger">{{ violations|length }}</h3>
                <p class="card-text">إجمالي الانتهاكات</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <i class="fas fa-chart-line fa-3x text-warning mb-3"></i>
                <h3 class="card-title text-warning">
                    {{ violations|selectattr("severity", "equalto", "عالي")|list|length }}
                </h3>
                <p class="card-text">انتهاكات عالية الخطورة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <i class="fas fa-calendar-week fa-3x text-info mb-3"></i>
                <h3 class="card-title text-info">
                    {{ violations|selectattr("created_at")|list|length }}
                </h3>
                <p class="card-text">انتهاكات هذا الأسبوع</p>
            </div>
        </div>
    </div>
</div>

<!-- Severity Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="btn-group" role="group" aria-label="فلترة حسب الخطورة">
                    <button type="button" class="btn btn-outline-secondary active" onclick="filterBySeverity('all')">
                        جميع الانتهاكات
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="filterBySeverity('عالي')">
                        عالي الخطورة
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="filterBySeverity('متوسط')">
                        متوسط الخطورة
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="filterBySeverity('منخفض')">
                        منخفض الخطورة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Violations List -->
<div class="row">
    <div class="col-12">
        {% if violations %}
            {% for violation in violations %}
            <div class="card mb-3 violation-item" data-severity="{{ violation.severity }}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <span class="badge violation-badge me-2">انتهاك</span>
                        <span class="badge 
                            {% if violation.severity == 'عالي' %}bg-danger
                            {% elif violation.severity == 'متوسط' %}bg-warning
                            {% else %}bg-info{% endif %}">
                            {{ violation.severity }}
                        </span>
                        <small class="text-muted ms-2">
                            <i class="fas fa-calendar me-1"></i>
                            {{ violation.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </small>
                    </div>
                    <div>
                        <small class="text-muted">
                            <i class="fas fa-source me-1"></i>
                            {{ violation.source }}
                        </small>
                    </div>
                </div>
                <div class="card-body">
                    <h5 class="card-title text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {{ violation.title }}
                    </h5>
                    <p class="card-text">
                        {{ violation.content[:400] }}...
                    </p>
                    
                    <!-- Violation Details -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="alert alert-danger" role="alert">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-1"></i>
                                    تفاصيل الانتهاك
                                </h6>
                                <hr>
                                <p class="mb-0">
                                    <strong>نوع الانتهاك:</strong> {{ violation.category }}<br>
                                    <strong>مستوى الخطورة:</strong> {{ violation.severity }}<br>
                                    <strong>تاريخ الرصد:</strong> {{ violation.created_at.strftime('%Y-%m-%d') }}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-grid gap-2">
                                <a href="{{ violation.url }}" target="_blank" class="btn btn-outline-primary">
                                    <i class="fas fa-external-link-alt me-1"></i>
                                    عرض المصدر الأصلي
                                </a>
                                <button class="btn btn-outline-warning" onclick="generateReport({{ violation.id }})">
                                    <i class="fas fa-file-alt me-1"></i>
                                    إنشاء تقرير
                                </button>
                                <button class="btn btn-outline-success" onclick="markAsResolved({{ violation.id }})">
                                    <i class="fas fa-check me-1"></i>
                                    تم الحل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-shield-alt fa-4x text-success mb-3"></i>
                        <h4 class="text-success">لا توجد انتهاكات مرصودة</h4>
                        <p class="text-muted">هذا أمر جيد! لم يتم رصد أي انتهاكات إعلامية حتى الآن</p>
                        <a href="/news" class="btn btn-primary">
                            <i class="fas fa-newspaper me-1"></i>
                            عرض جميع الأخبار
                        </a>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Export Options -->
{% if violations %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-download me-2"></i>
                    تصدير البيانات
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-success w-100" onclick="exportToExcel()">
                            <i class="fas fa-file-excel me-2"></i>
                            تصدير إلى Excel
                        </button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-danger w-100" onclick="exportToPDF()">
                            <i class="fas fa-file-pdf me-2"></i>
                            تصدير إلى PDF
                        </button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-info w-100" onclick="generateSummaryReport()">
                            <i class="fas fa-chart-bar me-2"></i>
                            تقرير إحصائي
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
function filterBySeverity(severity) {
    const cards = document.querySelectorAll('[data-severity]');
    const buttons = document.querySelectorAll('.btn-group button');
    
    // إزالة الحالة النشطة من جميع الأزرار
    buttons.forEach(btn => btn.classList.remove('active'));
    
    // إضافة الحالة النشطة للزر المحدد
    event.target.classList.add('active');
    
    cards.forEach(card => {
        if (severity === 'all' || card.dataset.severity === severity) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

async function markAsResolved(violationId) {
    if (!confirm('هل أنت متأكد من تحديد هذا الانتهاك كمحلول؟')) {
        return;
    }
    
    try {
        // هنا يمكن إضافة API call لتحديث حالة الانتهاك
        alert('تم تحديد الانتهاك كمحلول بنجاح');
        location.reload();
    } catch (error) {
        alert('حدث خطأ أثناء تحديث الانتهاك');
    }
}

function generateReport(violationId) {
    alert('سيتم إنشاء تقرير مفصل للانتهاك رقم ' + violationId);
    // يمكن إضافة منطق إنشاء التقرير هنا
}

function exportToExcel() {
    alert('سيتم تصدير البيانات إلى ملف Excel');
    // يمكن إضافة منطق التصدير هنا
}

function exportToPDF() {
    alert('سيتم تصدير البيانات إلى ملف PDF');
    // يمكن إضافة منطق التصدير هنا
}

function generateSummaryReport() {
    alert('سيتم إنشاء تقرير إحصائي شامل');
    // يمكن إضافة منطق التقرير الإحصائي هنا
}
</script>
{% endblock %}
