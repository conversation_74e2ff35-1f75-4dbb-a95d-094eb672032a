{% extends "base.html" %}

{% block title %}تقارير الانتهاكات - الرصد الإعلامي{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-gradient-info text-white">
            <div class="card-body">
                <h2 class="card-title">
                    <i class="fas fa-chart-bar me-2"></i>
                    تقارير الانتهاكات
                </h2>
                <p class="card-text">إحصائيات وتقارير شاملة عن انتهاكات حقوق الإنسان</p>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات عامة -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                <h3 class="card-title text-danger">{{ total_violations }}</h3>
                <p class="card-text">إجمالي الانتهاكات</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <i class="fas fa-fire fa-3x text-warning mb-3"></i>
                <h3 class="card-title text-warning">{{ severity_stats.get('حرج', 0) }}</h3>
                <p class="card-text">انتهاكات حرجة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <i class="fas fa-thermometer-full fa-3x text-danger mb-3"></i>
                <h3 class="card-title text-danger">{{ severity_stats.get('عالي', 0) }}</h3>
                <p class="card-text">انتهاكات عالية</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <i class="fas fa-thermometer-half fa-3x text-info mb-3"></i>
                <h3 class="card-title text-info">{{ severity_stats.get('متوسط', 0) }}</h3>
                <p class="card-text">انتهاكات متوسطة</p>
            </div>
        </div>
    </div>
</div>

<!-- مخطط الانتهاكات حسب مستوى الخطورة -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع الانتهاكات حسب مستوى الخطورة
                </h5>
            </div>
            <div class="card-body">
                <canvas id="severityChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    الانتهاكات حسب ملفات حقوق الإنسان
                </h5>
            </div>
            <div class="card-body">
                <canvas id="categoriesChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- ملفات حقوق الإنسان -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-folder-open me-2"></i>
                    ملفات حقوق الإنسان
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for category in categories_stats %}
                    <div class="col-md-6 mb-3">
                        <div class="card border-0" style="border-right: 4px solid {{ category.color }} !important;">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <i class="{{ category.icon }} fa-2x me-3" style="color: {{ category.color }};"></i>
                                        <div>
                                            <h6 class="card-title mb-1">{{ category.name }}</h6>
                                            <p class="card-text small text-muted">{{ category.description }}</p>
                                        </div>
                                    </div>
                                    <div class="text-center">
                                        <h4 class="text-primary mb-0">0</h4>
                                        <small class="text-muted">انتهاك</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تقارير تفصيلية -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    تقارير تفصيلية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-primary w-100" onclick="generateReport('monthly')">
                            <i class="fas fa-calendar-alt me-2"></i>
                            تقرير شهري
                        </button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-success w-100" onclick="generateReport('quarterly')">
                            <i class="fas fa-calendar me-2"></i>
                            تقرير ربع سنوي
                        </button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-info w-100" onclick="generateReport('annual')">
                            <i class="fas fa-calendar-check me-2"></i>
                            تقرير سنوي
                        </button>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-warning w-100" onclick="exportData('excel')">
                            <i class="fas fa-file-excel me-2"></i>
                            تصدير Excel
                        </button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-danger w-100" onclick="exportData('pdf')">
                            <i class="fas fa-file-pdf me-2"></i>
                            تصدير PDF
                        </button>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button class="btn btn-secondary w-100" onclick="exportData('csv')">
                            <i class="fas fa-file-csv me-2"></i>
                            تصدير CSV
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر التقارير -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    فلاتر التقارير
                </h5>
            </div>
            <div class="card-body">
                <form id="reportFilters">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="dateFrom" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="dateFrom" name="date_from">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="dateTo" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="dateTo" name="date_to">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="categoryFilter" class="form-label">ملف حقوق الإنسان</label>
                            <select class="form-select" id="categoryFilter" name="category">
                                <option value="">جميع الملفات</option>
                                {% for category in categories_stats %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="severityFilter" class="form-label">مستوى الخطورة</label>
                            <select class="form-select" id="severityFilter" name="severity">
                                <option value="">جميع المستويات</option>
                                <option value="حرج">حرج</option>
                                <option value="عالي">عالي</option>
                                <option value="متوسط">متوسط</option>
                                <option value="منخفض">منخفض</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>
                                تطبيق الفلاتر
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetFilters()">
                                <i class="fas fa-undo me-2"></i>
                                إعادة تعيين
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// بيانات مستوى الخطورة
const severityData = {
    labels: ['حرج', 'عالي', 'متوسط', 'منخفض'],
    datasets: [{
        data: [
            {{ severity_stats.get('حرج', 0) }},
            {{ severity_stats.get('عالي', 0) }},
            {{ severity_stats.get('متوسط', 0) }},
            {{ severity_stats.get('منخفض', 0) }}
        ],
        backgroundColor: [
            '#dc3545',
            '#fd7e14',
            '#ffc107',
            '#20c997'
        ],
        borderWidth: 2
    }]
};

// مخطط مستوى الخطورة
const severityCtx = document.getElementById('severityChart').getContext('2d');
new Chart(severityCtx, {
    type: 'doughnut',
    data: severityData,
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            },
            title: {
                display: true,
                text: 'توزيع الانتهاكات حسب مستوى الخطورة'
            }
        }
    }
});

// بيانات الفئات
const categoriesData = {
    labels: [
        {% for category in categories_stats %}
        '{{ category.name }}'{% if not loop.last %},{% endif %}
        {% endfor %}
    ],
    datasets: [{
        label: 'عدد الانتهاكات',
        data: [
            {% for category in categories_stats %}
            0{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        backgroundColor: [
            {% for category in categories_stats %}
            '{{ category.color }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        borderWidth: 1
    }]
};

// مخطط الفئات
const categoriesCtx = document.getElementById('categoriesChart').getContext('2d');
new Chart(categoriesCtx, {
    type: 'bar',
    data: categoriesData,
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                display: false
            },
            title: {
                display: true,
                text: 'الانتهاكات حسب ملفات حقوق الإنسان'
            }
        }
    }
});

// وظائف التقارير
function generateReport(type) {
    alert(`سيتم إنشاء التقرير ${type === 'monthly' ? 'الشهري' : type === 'quarterly' ? 'الربع سنوي' : 'السنوي'}`);
}

function exportData(format) {
    alert(`سيتم تصدير البيانات بصيغة ${format.toUpperCase()}`);
}

function resetFilters() {
    document.getElementById('reportFilters').reset();
}

// تطبيق الفلاتر
document.getElementById('reportFilters').addEventListener('submit', function(e) {
    e.preventDefault();
    alert('سيتم تطبيق الفلاتر وتحديث التقارير');
});
</script>
{% endblock %}
