/* الرصد الإعلامي - ملف الأنماط المخصص */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --font-family: 'Cairo', sans-serif;
}

/* تحسينات عامة */
body {
    font-family: var(--font-family);
    line-height: 1.6;
    background-color: var(--light-color);
}

/* تحسينات البطاقات */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.card:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
}

.stats-card .card-body {
    padding: 2rem;
}

/* عناصر الأخبار */
.news-item {
    border-right: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.news-item:hover {
    border-right-color: var(--info-color);
    background-color: #f8f9ff;
}

/* عناصر الانتهاكات */
.violation-item {
    border-right: 4px solid var(--danger-color);
    background-color: #fff5f5;
}

.violation-item:hover {
    background-color: #ffe6e6;
}

.violation-badge {
    background-color: var(--danger-color);
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
}

/* تحسينات شريط التنقل */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 0.5rem;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* تحسينات الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
    border: none;
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
    border: none;
}

.btn-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    border: none;
    color: #212529;
}

/* تحسينات النماذج */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسينات التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-warning {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-info {
    background: linear-gradient(45deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* تحسينات الشارات */
.badge {
    font-size: 0.75rem;
    padding: 0.35rem 0.65rem;
    border-radius: 10px;
}

/* تحسينات التذييل */
footer {
    margin-top: auto;
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .stats-card .card-body {
        padding: 1.5rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 8px !important;
        margin-bottom: 0.5rem;
    }
}

/* تحسينات إضافية للنصوص العربية */
.text-arabic {
    font-family: var(--font-family);
    text-align: right;
    direction: rtl;
}

/* تحسينات للجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    color: white;
    border: none;
    font-weight: 600;
}

/* تحسينات للمودال */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* تأثيرات خاصة */
.glow {
    box-shadow: 0 0 20px rgba(0, 123, 255, 0.5);
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}
