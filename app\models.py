from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List

# نماذج Pydantic للتحقق من البيانات

class NewsArticleBase(BaseModel):
    title: str
    content: str
    source: str
    url: str
    category: Optional[str] = "عام"
    is_classified: Optional[bool] = False
    classification_level: Optional[str] = "منخفض"

class NewsArticleCreate(NewsArticleBase):
    pass

class NewsArticle(NewsArticleBase):
    id: int
    published_date: datetime
    created_at: datetime
    
    class Config:
        from_attributes = True

class MediaSourceBase(BaseModel):
    name: str
    url: str
    rss_feed: Optional[str] = None
    is_active: Optional[bool] = True

class MediaSourceCreate(MediaSourceBase):
    pass

class MediaSource(MediaSourceBase):
    id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

# نموذج للبحث والفلترة
class NewsFilter(BaseModel):
    category: Optional[str] = None
    source: Optional[str] = None
    is_classified: Optional[bool] = None
    classification_level: Optional[str] = None
    search_term: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
