#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد نظام تصنيف الانتهاكات حسب ملفات حقوق الإنسان
"""

import sys
import os
import json
from datetime import datetime

# إضافة مسار التطبيق
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal, create_tables, HumanRightsCategory, ViolationType

def setup_human_rights_system():
    """إعداد نظام تصنيف حقوق الإنسان"""
    
    # إنشاء قاعدة البيانات
    create_tables()
    
    db = SessionLocal()
    
    try:
        # ملفات حقوق الإنسان الرئيسية
        categories = [
            {
                "name": "الحق في الحياة والأمان الشخصي",
                "description": "الحق الأساسي في الحياة وحماية الأمان الشخصي",
                "icon": "fas fa-shield-alt",
                "color": "#dc3545"
            },
            {
                "name": "الحق في الحرية والأمان",
                "description": "الحق في الحرية الشخصية والأمان من الاعتقال التعسفي",
                "icon": "fas fa-unlock",
                "color": "#fd7e14"
            },
            {
                "name": "الحق في الصحة والخدمات الطبية",
                "description": "الحق في الحصول على الرعاية الصحية والخدمات الطبية",
                "icon": "fas fa-heartbeat",
                "color": "#198754"
            },
            {
                "name": "حقوق الطفل",
                "description": "الحقوق الخاصة بالأطفال وحمايتهم",
                "icon": "fas fa-child",
                "color": "#0dcaf0"
            },
            {
                "name": "الحق في بيئة آمنة",
                "description": "الحق في العيش في بيئة نظيفة وآمنة",
                "icon": "fas fa-leaf",
                "color": "#20c997"
            },
            {
                "name": "الحق في التظاهر والتعبير",
                "description": "حرية التعبير والتجمع السلمي",
                "icon": "fas fa-bullhorn",
                "color": "#6f42c1"
            },
            {
                "name": "الحق في الحماية من العنف",
                "description": "الحماية من جميع أشكال العنف والاعتداء",
                "icon": "fas fa-hand-paper",
                "color": "#e83e8c"
            },
            {
                "name": "حقوق النازحين واللاجئين",
                "description": "حقوق النازحين واللاجئين والحماية الدولية",
                "icon": "fas fa-home",
                "color": "#6c757d"
            },
            {
                "name": "الحق في الحياة الكريمة",
                "description": "الحق في مستوى معيشي لائق",
                "icon": "fas fa-heart",
                "color": "#ffc107"
            },
            {
                "name": "النزاعات والصراعات",
                "description": "القضايا المتعلقة بالنزاعات المسلحة والصراعات",
                "icon": "fas fa-exclamation-triangle",
                "color": "#495057"
            }
        ]
        
        # إضافة الفئات الرئيسية
        category_ids = {}
        for cat_data in categories:
            existing = db.query(HumanRightsCategory).filter(
                HumanRightsCategory.name == cat_data["name"]
            ).first()
            
            if not existing:
                category = HumanRightsCategory(**cat_data)
                db.add(category)
                db.flush()
                category_ids[cat_data["name"]] = category.id
            else:
                category_ids[cat_data["name"]] = existing.id
        
        db.commit()
        
        # أنواع الانتهاكات مع الحقول المطلوبة
        violations = [
            # الحق في الحياة والأمان الشخصي
            {
                "name": "جرائم القتل",
                "category": "الحق في الحياة والأمان الشخصي",
                "description": "جرائم القتل العمد وغير العمد",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "مكان الحادث", "required": True},
                    "victims_count": {"type": "number", "label": "عدد الضحايا", "required": True},
                    "victims_details": {"type": "textarea", "label": "تفاصيل الضحايا", "required": False},
                    "weapon_used": {"type": "select", "label": "نوع السلاح", "options": ["سلاح ناري", "سلاح أبيض", "متفجرات", "أخرى"], "required": False},
                    "perpetrator": {"type": "text", "label": "الجاني", "required": False},
                    "motive": {"type": "textarea", "label": "الدافع", "required": False}
                })
            },
            {
                "name": "الانتحار",
                "category": "الحق في الحياة والأمان الشخصي",
                "description": "حالات الانتحار",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "مكان الحادث", "required": True},
                    "age": {"type": "number", "label": "عمر المتوفى", "required": False},
                    "gender": {"type": "select", "label": "الجنس", "options": ["ذكر", "أنثى"], "required": False},
                    "method": {"type": "select", "label": "طريقة الانتحار", "options": ["شنق", "سقوط", "سم", "سلاح ناري", "أخرى"], "required": False},
                    "possible_reasons": {"type": "textarea", "label": "الأسباب المحتملة", "required": False}
                })
            },
            {
                "name": "الإعدام",
                "category": "الحق في الحياة والأمان الشخصي",
                "description": "أحكام وتنفيذ الإعدام",
                "required_fields": json.dumps({
                    "execution_date": {"type": "date", "label": "تاريخ التنفيذ", "required": True},
                    "location": {"type": "text", "label": "مكان التنفيذ", "required": True},
                    "victims_count": {"type": "number", "label": "عدد المعدومين", "required": True},
                    "charges": {"type": "textarea", "label": "التهم", "required": False},
                    "court": {"type": "text", "label": "المحكمة", "required": False},
                    "execution_method": {"type": "select", "label": "طريقة الإعدام", "options": ["شنق", "رمي بالرصاص", "أخرى"], "required": False}
                })
            },
            
            # الحق في الحرية والأمان
            {
                "name": "الاعتقال",
                "category": "الحق في الحرية والأمان",
                "description": "حالات الاعتقال والاحتجاز",
                "required_fields": json.dumps({
                    "arrest_date": {"type": "date", "label": "تاريخ الاعتقال", "required": True},
                    "location": {"type": "text", "label": "مكان الاعتقال", "required": True},
                    "detainees_count": {"type": "number", "label": "عدد المعتقلين", "required": True},
                    "arresting_authority": {"type": "text", "label": "الجهة المعتقلة", "required": False},
                    "charges": {"type": "textarea", "label": "التهم", "required": False},
                    "detention_center": {"type": "text", "label": "مركز الاحتجاز", "required": False}
                })
            },
            
            # الحق في الصحة
            {
                "name": "اعتداء على الكوادر الطبية",
                "category": "الحق في الصحة والخدمات الطبية",
                "description": "الاعتداءات على الطاقم الطبي",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "مكان الحادث", "required": True},
                    "victims_count": {"type": "number", "label": "عدد المتضررين", "required": True},
                    "victim_profession": {"type": "select", "label": "مهنة الضحية", "options": ["طبيب", "ممرض", "صيدلي", "فني طبي", "أخرى"], "required": False},
                    "attack_type": {"type": "select", "label": "نوع الاعتداء", "options": ["جسدي", "لفظي", "تهديد", "منع من العمل"], "required": False},
                    "perpetrator": {"type": "text", "label": "الجاني", "required": False}
                })
            },
            
            # حقوق الطفل
            {
                "name": "انتهاكات ضد الأطفال",
                "category": "حقوق الطفل",
                "description": "جميع أشكال الانتهاكات ضد الأطفال",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "مكان الحادث", "required": True},
                    "victims_count": {"type": "number", "label": "عدد الأطفال المتضررين", "required": True},
                    "age_range": {"type": "text", "label": "الفئة العمرية", "required": False},
                    "violation_type": {"type": "select", "label": "نوع الانتهاك", "options": ["عنف جسدي", "عنف جنسي", "إهمال", "عمالة أطفال", "تجنيد", "أخرى"], "required": True},
                    "perpetrator": {"type": "text", "label": "الجاني", "required": False},
                    "relationship_to_child": {"type": "select", "label": "علاقة الجاني بالطفل", "options": ["والد", "قريب", "معلم", "غريب", "أخرى"], "required": False}
                })
            },

            # المزيد من الانتهاكات
            {
                "name": "النزاعات",
                "category": "النزاعات والصراعات",
                "description": "النزاعات المسلحة والصراعات",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "مكان النزاع", "required": True},
                    "parties_involved": {"type": "textarea", "label": "الأطراف المتنازعة", "required": True},
                    "casualties": {"type": "number", "label": "عدد الضحايا", "required": False},
                    "conflict_type": {"type": "select", "label": "نوع النزاع", "options": ["مسلح", "سياسي", "قبلي", "طائفي"], "required": False}
                })
            },
            {
                "name": "العنف الأسري",
                "category": "الحق في الحماية من العنف",
                "description": "حالات العنف داخل الأسرة",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "مكان الحادث", "required": True},
                    "victim_age": {"type": "number", "label": "عمر الضحية", "required": False},
                    "victim_gender": {"type": "select", "label": "جنس الضحية", "options": ["ذكر", "أنثى"], "required": False},
                    "violence_type": {"type": "select", "label": "نوع العنف", "options": ["جسدي", "نفسي", "جنسي", "اقتصادي"], "required": True},
                    "perpetrator_relation": {"type": "select", "label": "علاقة الجاني", "options": ["زوج", "والد", "أخ", "قريب", "أخرى"], "required": False}
                })
            },
            {
                "name": "النازحين والعودة الطوعية والخدمات المقدمة لهم",
                "category": "حقوق النازحين واللاجئين",
                "description": "قضايا النزوح والعودة والخدمات",
                "required_fields": json.dumps({
                    "displacement_location": {"type": "text", "label": "مكان النزوح", "required": True},
                    "return_location": {"type": "text", "label": "مكان العودة", "required": False},
                    "displaced_count": {"type": "number", "label": "عدد النازحين", "required": False},
                    "services_provided": {"type": "textarea", "label": "الخدمات المقدمة", "required": False},
                    "displacement_reason": {"type": "select", "label": "سبب النزوح", "options": ["نزاع مسلح", "كارثة طبيعية", "اضطهاد", "أخرى"], "required": False}
                })
            },
            {
                "name": "حوادث السير",
                "category": "الحق في الحياة والأمان الشخصي",
                "description": "حوادث المرور والسير",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "مكان الحادث", "required": True},
                    "casualties": {"type": "number", "label": "عدد الضحايا", "required": True},
                    "injured": {"type": "number", "label": "عدد المصابين", "required": False},
                    "accident_type": {"type": "select", "label": "نوع الحادث", "options": ["تصادم", "انقلاب", "دهس", "أخرى"], "required": False},
                    "vehicles_involved": {"type": "number", "label": "عدد المركبات", "required": False}
                })
            },
            {
                "name": "الأوبئة",
                "category": "الحق في الصحة والخدمات الطبية",
                "description": "انتشار الأمراض والأوبئة",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "المنطقة المتأثرة", "required": True},
                    "disease_name": {"type": "text", "label": "اسم المرض", "required": True},
                    "cases_count": {"type": "number", "label": "عدد الحالات", "required": False},
                    "deaths_count": {"type": "number", "label": "عدد الوفيات", "required": False},
                    "containment_measures": {"type": "textarea", "label": "إجراءات الاحتواء", "required": False}
                })
            },
            {
                "name": "التربية والتعليم",
                "category": "حقوق الطفل",
                "description": "قضايا التعليم والتربية",
                "required_fields": json.dumps({
                    "location": {"type": "text", "label": "المؤسسة التعليمية", "required": True},
                    "students_affected": {"type": "number", "label": "عدد الطلاب المتأثرين", "required": False},
                    "issue_type": {"type": "select", "label": "نوع المشكلة", "options": ["نقص في المعلمين", "تدهور البنية التحتية", "انقطاع الدراسة", "عنف في المدرسة"], "required": True},
                    "education_level": {"type": "select", "label": "المرحلة التعليمية", "options": ["ابتدائي", "متوسط", "ثانوي", "جامعي"], "required": False}
                })
            }
        ]

        # إضافة أنواع الانتهاكات
        for viol_data in violations:
            category_name = viol_data.pop("category")
            category_id = category_ids.get(category_name)
            
            if category_id:
                existing = db.query(ViolationType).filter(
                    ViolationType.name == viol_data["name"]
                ).first()
                
                if not existing:
                    violation = ViolationType(
                        category_id=category_id,
                        **viol_data
                    )
                    db.add(violation)
        
        db.commit()
        
        # طباعة الإحصائيات
        categories_count = db.query(HumanRightsCategory).count()
        violations_count = db.query(ViolationType).count()
        
        print(f"\n✅ تم إعداد نظام تصنيف الانتهاكات بنجاح!")
        print(f"📂 ملفات حقوق الإنسان: {categories_count}")
        print(f"🏷️ أنواع الانتهاكات: {violations_count}")
        print(f"\n🌐 يمكنك الآن استخدام النظام الجديد!")
        
    except Exception as e:
        print(f"❌ خطأ في إعداد النظام: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 بدء إعداد نظام تصنيف الانتهاكات...")
    setup_human_rights_system()
