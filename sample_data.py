#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف إضافة البيانات التجريبية للرصد الإعلامي
"""

import sys
import os
from datetime import datetime, timedelta
import random

# إضافة مسار التطبيق
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import SessionLocal, create_tables, NewsArticle, MediaSource

def create_sample_data():
    """إنشاء بيانات تجريبية للتطبيق"""
    
    # إنشاء قاعدة البيانات
    create_tables()
    
    db = SessionLocal()
    
    try:
        # إضافة مصادر إعلامية تجريبية
        sample_sources = [
            {
                "name": "الجزيرة",
                "url": "https://aljazeera.net",
                "rss_feed": "https://aljazeera.net/rss.xml",
                "is_active": True
            },
            {
                "name": "العربية",
                "url": "https://alarabiya.net",
                "rss_feed": "https://alarabiya.net/rss.xml",
                "is_active": True
            },
            {
                "name": "بي بي سي عربي",
                "url": "https://bbc.com/arabic",
                "rss_feed": "https://feeds.bbci.co.uk/arabic/rss.xml",
                "is_active": True
            },
            {
                "name": "سكاي نيوز عربية",
                "url": "https://skynewsarabia.com",
                "rss_feed": "https://skynewsarabia.com/rss.xml",
                "is_active": True
            },
            {
                "name": "الشرق الأوسط",
                "url": "https://aawsat.com",
                "rss_feed": "https://aawsat.com/rss.xml",
                "is_active": True
            }
        ]
        
        # إضافة المصادر إلى قاعدة البيانات
        for source_data in sample_sources:
            # التحقق من عدم وجود المصدر مسبقاً
            existing_source = db.query(MediaSource).filter(
                MediaSource.name == source_data["name"]
            ).first()
            
            if not existing_source:
                source = MediaSource(**source_data)
                db.add(source)
        
        db.commit()
        print("✅ تم إضافة المصادر الإعلامية بنجاح")
        
        # إضافة أخبار تجريبية
        sample_news = [
            {
                "title": "تطورات جديدة في الأوضاع السياسية بالمنطقة",
                "content": "شهدت المنطقة تطورات سياسية مهمة خلال الأيام الماضية، حيث عقدت عدة اجتماعات رفيعة المستوى لبحث القضايا الإقليمية والدولية. وأكدت المصادر الدبلوماسية أن هناك توجهاً نحو تعزيز التعاون في مختلف المجالات.",
                "source": "الجزيرة",
                "url": "https://aljazeera.net/news/politics/2024/01/15/political-developments",
                "category": "سياسة",
                "is_classified": False,
                "classification_level": "منخفض"
            },
            {
                "title": "انتهاك صارخ لحقوق الإنسان في منطقة النزاع",
                "content": "وثقت منظمات حقوقية دولية انتهاكات جسيمة لحقوق الإنسان في منطقة النزاع، تشمل استهداف المدنيين والمرافق الطبية. وطالبت هذه المنظمات بتحقيق دولي عاجل في هذه الانتهاكات.",
                "source": "بي بي سي عربي",
                "url": "https://bbc.com/arabic/news/2024/01/15/human-rights-violations",
                "category": "حقوق إنسان",
                "is_classified": True,
                "classification_level": "عالي"
            },
            {
                "title": "تقرير اقتصادي: نمو متوقع في القطاع التكنولوجي",
                "content": "أظهر تقرير اقتصادي حديث توقعات بنمو كبير في القطاع التكنولوجي خلال العام الجاري، مدفوعاً بالاستثمارات في الذكاء الاصطناعي والتحول الرقمي. ويتوقع الخبراء أن يساهم هذا النمو في خلق فرص عمل جديدة.",
                "source": "العربية",
                "url": "https://alarabiya.net/economy/2024/01/15/tech-growth",
                "category": "اقتصاد",
                "is_classified": False,
                "classification_level": "منخفض"
            },
            {
                "title": "تضليل إعلامي حول الأحداث الأخيرة",
                "content": "رصدت جهات مختصة حملة تضليل إعلامي منظمة تهدف إلى تشويه الحقائق حول الأحداث الأخيرة في المنطقة. وتشمل هذه الحملة نشر معلومات مغلوطة وصور مفبركة عبر وسائل التواصل الاجتماعي.",
                "source": "سكاي نيوز عربية",
                "url": "https://skynewsarabia.com/news/2024/01/15/media-disinformation",
                "category": "إعلام",
                "is_classified": True,
                "classification_level": "متوسط"
            },
            {
                "title": "إنجازات علمية جديدة في مجال الطب",
                "content": "حقق فريق من الباحثين العرب إنجازاً علمياً مهماً في مجال علاج السرطان، من خلال تطوير تقنية جديدة للعلاج المناعي. وتعد هذه النتائج بداية واعدة لعلاجات أكثر فعالية.",
                "source": "الشرق الأوسط",
                "url": "https://aawsat.com/science/2024/01/15/medical-breakthrough",
                "category": "علوم",
                "is_classified": False,
                "classification_level": "منخفض"
            },
            {
                "title": "خطاب كراهية في برنامج تلفزيوني",
                "content": "تضمن برنامج تلفزيوني بُث مؤخراً خطاباً يحرض على الكراهية ضد مجموعات عرقية ودينية معينة. وقد أثار هذا البرنامج انتقادات واسعة من منظمات المجتمع المدني والحقوقيين.",
                "source": "الجزيرة",
                "url": "https://aljazeera.net/news/media/2024/01/15/hate-speech-tv",
                "category": "إعلام",
                "is_classified": True,
                "classification_level": "عالي"
            },
            {
                "title": "مؤتمر دولي لمكافحة التغير المناخي",
                "content": "انطلق اليوم مؤتمر دولي رفيع المستوى لمناقشة استراتيجيات مكافحة التغير المناخي والتحول نحو الطاقة المتجددة. ويشارك في المؤتمر ممثلون من أكثر من 50 دولة ومنظمة دولية.",
                "source": "العربية",
                "url": "https://alarabiya.net/environment/2024/01/15/climate-conference",
                "category": "بيئة",
                "is_classified": False,
                "classification_level": "منخفض"
            },
            {
                "title": "انتهاك خصوصية المواطنين في تقرير إخباري",
                "content": "نشرت إحدى القنوات الإخبارية تقريراً يتضمن معلومات شخصية حساسة عن مواطنين دون موافقتهم، مما يشكل انتهاكاً واضحاً لقوانين حماية الخصوصية والبيانات الشخصية.",
                "source": "بي بي سي عربي",
                "url": "https://bbc.com/arabic/news/2024/01/15/privacy-violation",
                "category": "إعلام",
                "is_classified": True,
                "classification_level": "متوسط"
            },
            {
                "title": "مبادرة تعليمية جديدة لدعم الطلاب",
                "content": "أطلقت وزارة التعليم مبادرة جديدة تهدف إلى دعم الطلاب المتفوقين من خلال برامج تدريبية متخصصة ومنح دراسية. وتستهدف المبادرة طلاب المرحلتين الثانوية والجامعية.",
                "source": "سكاي نيوز عربية",
                "url": "https://skynewsarabia.com/education/2024/01/15/education-initiative",
                "category": "تعليم",
                "is_classified": False,
                "classification_level": "منخفض"
            },
            {
                "title": "تقرير متحيز ضد مجتمع معين",
                "content": "نشرت إحدى الصحف تقريراً يحتوي على تحيز واضح ضد مجتمع معين، مستخدمة لغة تمييزية ومعلومات غير دقيقة. وقد أدى هذا التقرير إلى تأجيج التوترات الاجتماعية في المنطقة.",
                "source": "الشرق الأوسط",
                "url": "https://aawsat.com/news/2024/01/15/biased-report",
                "category": "مجتمع",
                "is_classified": True,
                "classification_level": "عالي"
            }
        ]
        
        # إضافة الأخبار إلى قاعدة البيانات
        for i, news_data in enumerate(sample_news):
            # إضافة تواريخ متنوعة
            days_ago = random.randint(0, 30)
            hours_ago = random.randint(0, 23)
            
            news_data["published_date"] = datetime.now() - timedelta(days=days_ago, hours=hours_ago)
            news_data["created_at"] = news_data["published_date"]
            
            # التحقق من عدم وجود الخبر مسبقاً
            existing_news = db.query(NewsArticle).filter(
                NewsArticle.url == news_data["url"]
            ).first()
            
            if not existing_news:
                news = NewsArticle(**news_data)
                db.add(news)
        
        db.commit()
        print("✅ تم إضافة الأخبار التجريبية بنجاح")
        
        # طباعة إحصائيات
        total_news = db.query(NewsArticle).count()
        classifications_count = db.query(NewsArticle).filter(NewsArticle.is_classified == True).count()
        sources_count = db.query(MediaSource).count()

        print(f"\n📊 إحصائيات البيانات التجريبية:")
        print(f"   📰 إجمالي الأخبار: {total_news}")
        print(f"   🏷️  التصنيفات: {classifications_count}")
        print(f"   📡 المصادر: {sources_count}")
        print(f"\n🎉 تم إنشاء البيانات التجريبية بنجاح!")
        print(f"🌐 يمكنك الآن تشغيل التطبيق والوصول إليه على: http://localhost:8000")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 بدء إنشاء البيانات التجريبية للرصد الإعلامي...")
    create_sample_data()
