#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل تطبيق الرصد الإعلامي
"""

import uvicorn
import sys
import os
from pathlib import Path

def main():
    """تشغيل تطبيق الرصد الإعلامي"""
    
    print("🚀 بدء تشغيل تطبيق الرصد الإعلامي...")
    print("=" * 50)
    
    # التحقق من وجود ملف قاعدة البيانات
    db_file = Path("violations_news.db")
    if not db_file.exists():
        print("📊 لم يتم العثور على قاعدة البيانات...")
        print("🔧 سيتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل")
        
        # سؤال المستخدم عن إضافة البيانات التجريبية
        response = input("\n❓ هل تريد إضافة بيانات تجريبية؟ (y/n): ").lower().strip()
        if response in ['y', 'yes', 'نعم', 'ن']:
            try:
                print("📝 جاري إضافة البيانات التجريبية...")
                from sample_data import create_sample_data
                create_sample_data()
            except Exception as e:
                print(f"❌ خطأ في إضافة البيانات التجريبية: {e}")
                print("⚠️  سيتم تشغيل التطبيق بدون بيانات تجريبية")
    
    print("\n🌐 معلومات التطبيق:")
    print("   📍 العنوان: http://localhost:8000")
    print("   📱 واجهة المستخدم: Bootstrap 5 RTL")
    print("   🔧 إطار العمل: FastAPI")
    print("   💾 قاعدة البيانات: SQLite")
    
    print("\n📋 الصفحات المتاحة:")
    print("   🏠 الرئيسية: http://localhost:8000/")
    print("   📰 الأخبار: http://localhost:8000/news")
    print("   ⚠️  الانتهاكات: http://localhost:8000/violations")
    print("   📊 API: http://localhost:8000/docs")
    
    print("\n" + "=" * 50)
    print("🎯 لإيقاف التطبيق اضغط Ctrl+C")
    print("=" * 50)
    
    try:
        # تشغيل التطبيق
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            reload_dirs=["app", "static"],
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف التطبيق بنجاح!")
        print("شكراً لاستخدام نظام الرصد الإعلامي")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")
        print("💡 تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
