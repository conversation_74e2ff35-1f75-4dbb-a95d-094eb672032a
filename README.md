# الرصد الإعلامي - Media Monitoring System

نظام متطور لرصد وتحليل الأخبار والانتهاكات الإعلامية باستخدام أحدث التقنيات.

## 🚀 المميزات

- **رصد الأخبار**: جمع الأخبار تلقائياً من مصادر RSS متعددة
- **كشف الانتهاكات**: تحديد وتصنيف الانتهاكات الإعلامية
- **واجهة عربية**: تصميم متجاوب باللغة العربية مع Bootstrap 5
- **بحث متقدم**: فلترة وبحث في الأخبار حسب المصدر والفئة
- **إحصائيات**: لوحة تحكم مع إحصائيات شاملة
- **تصدير البيانات**: إمكانية تصدير التقارير بصيغ مختلفة

## 🛠️ التقنيات المستخدمة

### Backend
- **FastAPI**: إطار عمل Python سريع وحديث
- **SQLAlchemy**: ORM للتعامل مع قاعدة البيانات
- **Pydantic**: التحقق من صحة البيانات
- **SQLite**: قاعدة بيانات خفيفة

### Frontend
- **Bootstrap 5**: إطار عمل CSS متجاوب
- **Jinja2**: محرك القوالب
- **Font Awesome**: أيقونات
- **Google Fonts**: خط Cairo العربي

### مكتبات إضافية
- **feedparser**: قراءة RSS feeds
- **BeautifulSoup**: تحليل HTML
- **requests**: طلبات HTTP
- **newspaper3k**: استخراج محتوى الأخبار

## 📋 متطلبات النظام

- Python 3.8+
- pip (مدير حزم Python)

## 🔧 التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق

```bash
# تشغيل مع uvicorn
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# أو تشغيل مباشر
python -m app.main
```

### 3. فتح التطبيق

افتح المتصفح وانتقل إلى: `http://localhost:8000`

## 📁 هيكل المشروع

```
Violations_news/
├── app/
│   ├── __init__.py          # ملف التهيئة
│   ├── main.py              # التطبيق الرئيسي
│   ├── models.py            # نماذج البيانات
│   ├── database.py          # إعدادات قاعدة البيانات
│   └── templates/           # قوالب HTML
│       ├── base.html        # القالب الأساسي
│       ├── index.html       # الصفحة الرئيسية
│       ├── news.html        # صفحة الأخبار
│       └── violations.html  # صفحة الانتهاكات
├── static/
│   ├── css/
│   │   └── style.css        # ملف الأنماط المخصص
│   ├── js/
│   │   └── main.js          # ملف JavaScript الرئيسي
│   └── images/              # الصور
├── requirements.txt         # المكتبات المطلوبة
├── README.md               # هذا الملف
└── violations_news.db      # قاعدة البيانات (تُنشأ تلقائياً)
```

## 🔗 API Endpoints

### الأخبار
- `GET /` - الصفحة الرئيسية
- `GET /news` - صفحة الأخبار مع الفلترة
- `GET /api/news` - جلب الأخبار (JSON)
- `POST /api/news` - إضافة خبر جديد

### الانتهاكات
- `GET /violations` - صفحة الانتهاكات
- `PATCH /api/news/{id}/mark-violation` - تحديد مقال كانتهاك

### المصادر
- `POST /api/sources` - إضافة مصدر إعلامي جديد
- `POST /api/collect-news` - جمع الأخبار من المصادر

## 📊 استخدام النظام

### إضافة مصادر إعلامية

```python
# مثال على إضافة مصدر عبر API
import requests

source_data = {
    "name": "الجزيرة",
    "url": "https://aljazeera.net",
    "rss_feed": "https://aljazeera.net/rss.xml",
    "is_active": True
}

response = requests.post("http://localhost:8000/api/sources", json=source_data)
```

### جمع الأخبار

```bash
# استدعاء API لجمع الأخبار
curl -X POST http://localhost:8000/api/collect-news
```

## 🎨 التخصيص

### تغيير الألوان
عدّل متغيرات CSS في `static/css/style.css`:

```css
:root {
    --primary-color: #007bff;
    --danger-color: #dc3545;
    /* ... */
}
```

### إضافة مصادر جديدة
أضف مصادر RSS في قاعدة البيانات أو عبر واجهة الإدارة.

## 🔒 الأمان

- التحقق من صحة البيانات باستخدام Pydantic
- حماية من SQL Injection عبر SQLAlchemy
- تشفير كلمات المرور (للمستخدمين المستقبليين)

## 📈 التطوير المستقبلي

- [ ] نظام المستخدمين والصلاحيات
- [ ] تحليل المشاعر للأخبار
- [ ] إشعارات فورية للانتهاكات
- [ ] تطبيق موبايل
- [ ] تكامل مع وسائل التواصل الاجتماعي
- [ ] ذكاء اصطناعي لكشف الانتهاكات تلقائياً

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

## 📞 التواصل

- **المطور**: [اسمك]
- **البريد الإلكتروني**: [بريدك الإلكتروني]
- **GitHub**: [رابط GitHub]

## 🙏 شكر وتقدير

- Bootstrap Team لإطار العمل الرائع
- FastAPI Team للإطار السريع والحديث
- مجتمع Python للمكتبات المفيدة

---

**ملاحظة**: هذا المشروع في مرحلة التطوير. نرحب بالاقتراحات والتحسينات!
